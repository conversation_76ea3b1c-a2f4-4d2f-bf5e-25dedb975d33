import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { NextIntlClientProvider } from 'next-intl'
import AdminOrdersPage from '@/app/[locale]/admin/orders/page'

// Mock next/navigation
const mockPush = jest.fn()
const mockUseRouter = jest.fn(() => ({
  push: mockPush,
}))

jest.mock('next/navigation', () => ({
  useRouter: () => mockUseRouter(),
}))

// Mock next-intl
const mockUseLocale = jest.fn(() => 'it')
const mockUseTranslations = jest.fn(() => (key: string) => {
  const translations: Record<string, string> = {
    'admin.ordersPage.title': 'Gestione Ordini',
    'admin.ordersPage.subtitle': 'Gestisci tutti gli ordini dei clienti e il loro stato',
    'admin.ordersPage.searchPlaceholder': 'Cerca ordini...',
    'admin.ordersPage.noOrders': 'Nessun ordine disponibile',
    'admin.ordersPage.noOrdersFound': 'Nessun ordine trovato per la ricerca',
    'admin.ordersPage.totalOrders': 'Ordini Totali',
    'admin.ordersPage.pending': 'In Attesa',
    'admin.ordersPage.shipped': 'Spediti',
    'admin.ordersPage.delivered': 'Consegnati',
    'admin.ordersPage.allOrders': 'Tutti gli Ordini',
    'admin.ordersPage.order': 'Ordine',
    'admin.ordersPage.customer': 'Cliente',
    'admin.ordersPage.date': 'Data',
    'admin.ordersPage.amount': 'Importo',
    'admin.ordersPage.status': 'Stato',
    'admin.ordersPage.tracking': 'Tracking',
    'admin.ordersPage.actions': 'Azioni',
  }
  return translations[key] || key
})

jest.mock('next-intl', () => ({
  useLocale: () => mockUseLocale(),
  useTranslations: () => mockUseTranslations(),
}))

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(),
}

jest.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Mock fetch for API calls
global.fetch = jest.fn()

// Mock admin back button
jest.mock('@/components/admin/admin-back-button', () => ({
  AdminBackButton: () => <div data-testid="admin-back-button">Back to Dashboard</div>,
}))

const mockOrders = [
  {
    id: '1',
    order_number: 'ORD-001',
    total_amount: 2500,
    status: 'pending',
    created_at: '2024-01-15T10:00:00Z',
    shipping_address: { city: 'Milano', country: 'Italy' },
    tracking_number: 'TRK123456',
    email: '<EMAIL>',
    user_id: 'user-1',
    users: { first_name: 'Mario', last_name: 'Rossi', email: '<EMAIL>' },
  },
  {
    id: '2',
    order_number: 'ORD-002',
    total_amount: 3500,
    status: 'shipped',
    created_at: '2024-01-16T11:00:00Z',
    shipping_address: { city: 'Roma', country: 'Italy' },
    tracking_number: 'TRK789012',
    email: '<EMAIL>',
    user_id: 'user-2',
    users: { first_name: 'Giulia', last_name: 'Bianchi', email: '<EMAIL>' },
  },
  {
    id: '3',
    order_number: 'ORD-003',
    total_amount: 1800,
    status: 'delivered',
    created_at: '2024-01-17T12:00:00Z',
    shipping_address: { city: 'Napoli', country: 'Italy' },
    tracking_number: null,
    email: '<EMAIL>',
    user_id: null,
    users: null,
  },
]

describe('Admin Orders Search', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock successful auth check
    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: { id: 'admin-user', email: '<EMAIL>' } },
      error: null,
    })

    mockSupabaseClient.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { is_admin: true },
            error: null,
          }),
        }),
      }),
    })

    // Mock successful orders API response
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({
        success: true,
        orders: mockOrders,
      }),
    })
  })

  it('should render search input field', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Cerca ordini...')).toBeInTheDocument()
    })
  })

  it('should filter orders by order number', async () => {
    render(<AdminOrdersPage />)

    // Wait for orders to load
    await waitFor(() => {
      expect(screen.getByText('ORD-001')).toBeInTheDocument()
    })

    // Search for specific order number
    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'ORD-001' } })

    // Should show only matching order
    expect(screen.getByText('ORD-001')).toBeInTheDocument()
    expect(screen.queryByText('ORD-002')).not.toBeInTheDocument()
    expect(screen.queryByText('ORD-003')).not.toBeInTheDocument()
  })

  it('should filter orders by customer email', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'giulia.bianchi' } })

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.queryByText('<EMAIL>')).not.toBeInTheDocument()
  })

  it('should filter orders by customer name', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('Mario Rossi')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'Giulia' } })

    expect(screen.getByText('Giulia Bianchi')).toBeInTheDocument()
    expect(screen.queryByText('Mario Rossi')).not.toBeInTheDocument()
  })

  it('should filter orders by status', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('pending')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'shipped' } })

    // Should show only shipped orders
    expect(screen.getByText('shipped')).toBeInTheDocument()
    expect(screen.queryByText('pending')).not.toBeInTheDocument()
    expect(screen.queryByText('delivered')).not.toBeInTheDocument()
  })

  it('should filter orders by tracking number', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('TRK123456')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'TRK789012' } })

    expect(screen.getByText('TRK789012')).toBeInTheDocument()
    expect(screen.queryByText('TRK123456')).not.toBeInTheDocument()
  })

  it('should show "no orders found" message when search has no results', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('ORD-001')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    expect(screen.getByText('Nessun ordine trovato per la ricerca')).toBeInTheDocument()
    expect(screen.queryByText('ORD-001')).not.toBeInTheDocument()
  })

  it('should show all orders when search is cleared', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('ORD-001')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    
    // Search for something
    fireEvent.change(searchInput, { target: { value: 'ORD-001' } })
    expect(screen.queryByText('ORD-002')).not.toBeInTheDocument()

    // Clear search
    fireEvent.change(searchInput, { target: { value: '' } })
    
    // All orders should be visible again
    expect(screen.getByText('ORD-001')).toBeInTheDocument()
    expect(screen.getByText('ORD-002')).toBeInTheDocument()
    expect(screen.getByText('ORD-003')).toBeInTheDocument()
  })

  it('should perform case-insensitive search', async () => {
    render(<AdminOrdersPage />)

    await waitFor(() => {
      expect(screen.getByText('Mario Rossi')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Cerca ordini...')
    fireEvent.change(searchInput, { target: { value: 'MARIO' } })

    expect(screen.getByText('Mario Rossi')).toBeInTheDocument()
    expect(screen.queryByText('Giulia Bianchi')).not.toBeInTheDocument()
  })
})
